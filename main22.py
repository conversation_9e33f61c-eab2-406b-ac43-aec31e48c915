import streamlit as st
import os
from langchain_groq import Cha<PERSON><PERSON>ro<PERSON>
from langchain_community.embeddings import OllamaEmbeddings
from langchain_openai import OpenAIEmbeddings
from langchain_huggingface import HuggingFaceEmbeddings
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.chains.combine_documents import create_stuff_documents_chain
from langchain_core.prompts import ChatPromptTemplate
from langchain.chains import create_retrieval_chain
from langchain_community.vectorstores import FAISS
from langchain_community.document_loaders import PyPDFDirectoryLoader
import time
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Load the GROQ API Key
os.environ['GROQ_API_KEY'] = os.getenv("GROQ_API_KEY")
groq_api_key = os.getenv("GROQ_API_KEY")

# Initialize the LLM
llm = ChatGroq(groq_api_key=groq_api_key, model_name="Llama3-8b-8192")

# Create the prompt template
prompt = ChatPromptTemplate.from_template(
    """
    Answer the questions based on the provided context only.
    Please provide the most accurate response based on the question
    <context>
    {context}
    </context>
    Question: {input}
    """
)

def create_vector_embedding():
    if "vectors" not in st.session_state:
        try:
            # Option 1: Try Ollama first (requires Ollama to be installed and running)
            st.session_state.embeddings = OllamaEmbeddings(model="nomic-embed-text")
            # Test the connection
            test_embed = st.session_state.embeddings.embed_query("test")
            st.success("✅ Using Ollama embeddings successfully!")
        except Exception as e:
            st.warning(f"⚠️ Ollama not available: {str(e)[:100]}...")
            try:
                # Option 2: Try OpenAI embeddings (requires OpenAI API key)
                openai_api_key = os.getenv("OPENAI_API_KEY")
                if openai_api_key:
                    st.session_state.embeddings = OpenAIEmbeddings(openai_api_key=openai_api_key)
                    st.success("✅ Using OpenAI embeddings!")
                else:
                    raise Exception("OpenAI API key not found")
            except Exception as e2:
                # Option 3: Use HuggingFace embeddings (free, runs locally)
                st.session_state.embeddings = HuggingFaceEmbeddings(
                    model_name="sentence-transformers/all-MiniLM-L6-v2"
                )
                st.success("✅ Using HuggingFace embeddings!")
        
        st.session_state.loader = PyPDFDirectoryLoader("research_papers")  # Data Ingestion step
        st.session_state.docs = st.session_state.loader.load()  # Document Loading
        
        if not st.session_state.docs:
            st.error("❌ No PDF files found in 'research_papers' directory!")
            return
            
        st.session_state.text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)
        st.session_state.final_documents = st.session_state.text_splitter.split_documents(st.session_state.docs[:50])
        st.session_state.vectors = FAISS.from_documents(st.session_state.final_documents, st.session_state.embeddings)
        st.success(f"✅ Processed {len(st.session_state.final_documents)} document chunks!")

# Streamlit UI
st.title("RAG Document Q&A With Groq And Llama3")

user_prompt = st.text_input("Enter your query from the research paper")

if st.button("Document Embedding"):
    create_vector_embedding()
    st.write("Vector Database is ready")

if user_prompt:
    # Check if vectors are created
    if "vectors" not in st.session_state:
        st.error("Please create document embeddings first by clicking 'Document Embedding' button")
    else:
        document_chain = create_stuff_documents_chain(llm, prompt)
        retriever = st.session_state.vectors.as_retriever()
        retrieval_chain = create_retrieval_chain(retriever, document_chain)
        
        start = time.process_time()
        response = retrieval_chain.invoke({'input': user_prompt})
        print(f"Response time: {time.process_time() - start}")
        
        st.write(response['answer'])
        
        # With a streamlit expander
        with st.expander("Document similarity Search"):
            for i, doc in enumerate(response['context']):
                st.write(doc.page_content)
                st.write('------------------------')