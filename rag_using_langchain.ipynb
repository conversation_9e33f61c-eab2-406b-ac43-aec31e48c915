import os
os.environ["GROQ_API_KEY"] = "********************************************************"

from youtube_transcript_api import YouTubeTranscript<PERSON>pi, TranscriptsDisabled
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_huggingface import HuggingFaceEmbeddings
from langchain_groq import ChatGroq
from langchain_community.vectorstores import FAISS
from langchain_core.prompts import PromptTemplate

video_id = "ZzaPdXTrSb8" # only the ID, not full URL
try:
    # If you don’t care which language, this returns the “best” one
    transcript_list = YouTubeTranscriptApi.get_transcript(video_id, languages=["en"])

    # Flatten it to plain text
    transcript = " ".join(chunk["text"] for chunk in transcript_list)
    print(transcript)

except TranscriptsDisabled:
    print("No captions available for this video.")

# IMPROVED VERSION: Better error handling for YouTube transcripts
from youtube_transcript_api import NoTranscriptFound

def get_youtube_transcript(video_id):
    """Get YouTube transcript with better error handling"""
    try:
        # First, let's check what transcripts are available
        transcript_list_obj = YouTubeTranscriptApi.list_transcripts(video_id)
        print("Available transcripts:")
        for transcript in transcript_list_obj:
            print(f"- {transcript.language} ({transcript.language_code})")
        
        # Try to get English transcript first
        try:
            transcript_list = YouTubeTranscriptApi.get_transcript(video_id, languages=["en"])
            print("\nUsing English transcript")
        except NoTranscriptFound:
            # If English is not available, try other English variants
            try:
                transcript_list = transcript_list_obj.find_transcript(['en-US', 'en-GB', 'en-CA']).fetch()
                print("\nUsing English variant transcript")
            except:
                # If no English, get the first available transcript
                transcript_list = list(transcript_list_obj)[0].fetch()
                print("\nUsing first available transcript")

        # Flatten it to plain text
        transcript = " ".join(chunk["text"] for chunk in transcript_list)
        return transcript
        
    except Exception as e:
        print(f"Error getting transcript: {e}")
        return None

# Try different video IDs that are more likely to have English transcripts
video_options = [
    "aircAruvnKk",  # 3Blue1Brown - Neural Networks
    "bZQun8Y4L2A",  # Another educational video
    "R9OHn5ZF4Uo",  # TED talk
    "dQw4w9WgXcQ"   # Famous video (fallback)
]

transcript = None
for video_id in video_options:
    print(f"\nTrying video ID: {video_id}")
    transcript = get_youtube_transcript(video_id)
    if transcript:
        print(f"Success! Transcript length: {len(transcript)} characters")
        print(f"First 500 characters: {transcript[:500]}...")
        break
    else:
        print("Failed, trying next video...")

# If all videos fail, use sample text
if not transcript:
    print("\nAll videos failed. Using sample text for demonstration...")
    transcript = """Artificial intelligence is transforming the world around us. 
    Machine learning algorithms can now recognize images, understand speech, 
    and even generate human-like text. Deep learning, a subset of machine learning, 
    uses neural networks with multiple layers to learn complex patterns in data. 
    These systems have achieved remarkable success in various domains including 
    computer vision, natural language processing, and game playing. 
    The future of AI holds great promise for solving complex problems 
    and improving human lives. Neural networks are inspired by the human brain 
    and consist of interconnected nodes that process information. 
    Training these networks requires large amounts of data and computational power."""

transcript_list

splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)
chunks = splitter.create_documents([transcript])

len(chunks)

chunks[12]

# Using free HuggingFace embeddings instead of OpenAI
# all-MiniLM-L6-v2 is a lightweight, fast, and effective sentence transformer model
embeddings = HuggingFaceEmbeddings(model_name="all-MiniLM-L6-v2")
vector_store = FAISS.from_documents(chunks, embeddings)

vector_store.index_to_docstore_id

vector_store.get_by_ids(['78a0b107-c31d-4cbc-90d5-e3080ec7b5a6'])

retriever = vector_store.as_retriever(search_type="similarity", search_kwargs={"k": 4})

retriever

retriever.invoke('What is deepmind')

llm = ChatGroq(model="llama-3.1-70b-versatile", temperature=0.2)

prompt = PromptTemplate(
    template="""
      You are a helpful assistant.
      Answer ONLY from the provided transcript context.
      If the context is insufficient, just say you don't know.

      {context}
      Question: {question}
    """,
    input_variables = ['context', 'question']
)

question          = "is the topic of nuclear fusion discussed in this video? if yes then what was discussed"
retrieved_docs    = retriever.invoke(question)

retrieved_docs

context_text = "\n\n".join(doc.page_content for doc in retrieved_docs)
context_text

final_prompt = prompt.invoke({"context": context_text, "question": question})

final_prompt

answer = llm.invoke(final_prompt)
print(answer.content)

from langchain_core.runnables import RunnableParallel, RunnablePassthrough, RunnableLambda
from langchain_core.output_parsers import StrOutputParser

def format_docs(retrieved_docs):
  context_text = "\n\n".join(doc.page_content for doc in retrieved_docs)
  return context_text

parallel_chain = RunnableParallel({
    'context': retriever | RunnableLambda(format_docs),
    'question': RunnablePassthrough()
})

parallel_chain.invoke('who is Demis')

parser = StrOutputParser()

main_chain = parallel_chain | prompt | llm | parser

main_chain.invoke('Can you summarize the video')

